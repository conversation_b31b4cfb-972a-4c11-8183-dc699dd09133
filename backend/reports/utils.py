# from . import new_custom_reports as r
# from datetime import datetime


# def get_consumption_report(tank_id, start, end, report_type):
#     report_type = r.ConsumptionReportFactory().get_consumption_report_type(report_type)
#     return report_type(tank_id, start, end).get_consumption_report()


# def get_delivery_report(tank, start, end):
#     # temporary fix to get delivery within timerage less than jan 28
#     start_time = datetime.strptime(start, '%Y-%m-%d %H:%M')
#     end_time = datetime.strptime(end, '%Y-%m-%d %H:%M')
#     threshold = "2021-01-28 06:09"
#     threshold_time = datetime.strptime(threshold, "%Y-%m-%d %H:%M")
#     if (tank.Tank_controller == 'TLS' and end_time > threshold_time):
#         temp_report = []
#         if (start_time < end_time):
#             report_type = r.DeliveryReportFactory().get_delivery_report_type(tank.Tank_controller)
#             print(report_type)
#             temp_report.extend(report_type(
#                 # type: ignore
#                 tank.Tank_id, start, end).get_delivery_report())

#             report_type = r.DeliveryReportFactory().get_delivery_report_type('MTC')
#             deliveries = report_type(
#                 tank.Tank_id, start, end).get_delivery_report()
#             temp_report.extend(deliveries)

#         else:
#             report_type = r.DeliveryReportFactory().get_delivery_report_type('MTC')
#             deliveries = report_type(
#                 tank.Tank_id, start, end).get_delivery_report()
#             temp_report.extend(deliveries)
#         return temp_report

#     report_type = r.DeliveryReportFactory().get_delivery_report_type(tank.Tank_controller)
#     return report_type(tank.Tank_id, start, end).get_delivery_report()
from datetime import datetime
from . import new_custom_reports as r


def get_consumption_report(tank_id, start, end, report_type):
    report_class = r.ConsumptionReportFactory().get_consumption_report_type(report_type)
    return report_class(tank_id, start, end).get_consumption_report()


def get_delivery_report(tank, start, end):
    start_time = datetime.strptime(start, '%Y-%m-%d %H:%M')
    end_time = datetime.strptime(end, '%Y-%m-%d %H:%M')
    threshold_time = datetime.strptime("2021-01-28 06:09", "%Y-%m-%d %H:%M")

    # TLS controller with mixed logic after threshold
    if tank.Tank_controller == 'TLS' and end_time > threshold_time:
        return _mixed_tls_mtc_delivery(tank.Tank_id, start, end, start_time, end_time)

    # Normal delivery path
    report_class = r.DeliveryReportFactory(
    ).get_delivery_report_type(tank.Tank_controller)
    return report_class(tank.Tank_id, start, end).get_delivery_report()


def _mixed_tls_mtc_delivery(tank_id, start, end, start_time, end_time):
    deliveries = []

    if start_time < end_time:
        tls_class = r.DeliveryReportFactory().get_delivery_report_type('TLS')
        mtc_class = r.DeliveryReportFactory().get_delivery_report_type('MTC')

        deliveries.extend(tls_class(tank_id, start, end).get_delivery_report())
        deliveries.extend(mtc_class(tank_id, start, end).get_delivery_report())
    else:
        # fallback to MTC only
        mtc_class = r.DeliveryReportFactory().get_delivery_report_type('MTC')
        deliveries.extend(mtc_class(tank_id, start, end).get_delivery_report())

    return deliveries
